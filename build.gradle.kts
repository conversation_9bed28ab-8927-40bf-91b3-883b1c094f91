plugins {
    kotlin("jvm") version "2.2.0"
    application
}

group = "dev.halim"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

application {
    mainClass.set("MainKt")
}

dependencies {
//    implementation("io.insert-koin:koin-core:4.0.3")
    implementation("io.insert-koin:koin-core:4.1.0")
    testImplementation(kotlin("test"))
}

tasks.test {
    useJUnitPlatform()
}

kotlin {
    compilerOptions {
        freeCompilerArgs.add("-Xcontext-parameters")
    }

    jvmToolchain(23)
}