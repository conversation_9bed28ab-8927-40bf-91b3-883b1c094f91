import di.ConnectionFactoryDI
import di.RepositoryDI
import module.repositoryModule
import org.koin.core.context.startKoin
import org.koin.core.logger.EmptyLogger
import org.koin.core.logger.Level

fun main() {
    startKoin {
        modules(repositoryModule)
        printLogger(level = Level.DEBUG)
    }

    RepositoryDI.hello("السلام عليكم")

    ConnectionFactoryDI.createConnection("mysql")
    ConnectionFactoryDI.createConnection("oracle")
    ConnectionFactoryDI.createConnection("postgresql")
    ConnectionFactoryDI.createConnection("mongodb")
}