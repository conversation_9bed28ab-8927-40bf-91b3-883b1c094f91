package di

import component.ConnectionFactory
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.parameter.parametersOf

object ConnectionFactoryDI : KoinComponent {

    fun createConnection(driver: String) {
        val connectionFactory by inject<ConnectionFactory> { parametersOf(driver) }
        println(connectionFactory.getConnection())
    }
}