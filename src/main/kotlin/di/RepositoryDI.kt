package di

import component.Repository
import org.koin.core.component.KoinComponent
import org.koin.core.component.get
import org.koin.core.component.inject
import org.koin.core.qualifier.named

object RepositoryDI : KoinComponent {
    val repository by inject<Repository>()
    val pi by inject<Double>(qualifier = named("pi"))
    val pi2 = get<Double>(qualifier = named("pi"))

    fun hello(msg: String) {
        println("pi = $pi")
        println("pi2 = $pi2")
        repository.hello(msg)
    }
}