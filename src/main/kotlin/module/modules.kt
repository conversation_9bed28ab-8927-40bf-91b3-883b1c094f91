package module

import component.ConnectionFactory
import component.Repository
import component.RepositoryImpl
import org.koin.core.module.dsl.bind
import org.koin.core.module.dsl.createdAtStart
import org.koin.core.module.dsl.factoryOf
import org.koin.core.module.dsl.singleOf
import org.koin.core.module.dsl.withOptions
import org.koin.core.qualifier.named
import org.koin.dsl.bind

import org.koin.dsl.module

val repositoryModule = module {
    single(createdAtStart = true) { RepositoryImpl() } bind (Repository::class)

    single { RepositoryImpl() } withOptions {
        bind<Repository>()
        createdAtStart()
    }

    single<Repository> { RepositoryImpl() }

    singleOf(::RepositoryImpl) bind (Repository::class)

    singleOf(::RepositoryImpl) {
        bind<Repository>()
        createdAtStart()
    }

    factoryOf(::ConnectionFactory)

    single(qualifier = named("pi")) { 3.14 }
}